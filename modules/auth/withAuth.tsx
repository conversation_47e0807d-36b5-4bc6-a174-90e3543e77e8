import { useRouter } from 'next/router';
import React, { useEffect, useState, useCallback } from 'react';
import { useAppSelector, useAppDispatch } from 'store/hooks/index';
import Loading from '@/modules/common/loader';
import { useSession, getSession, signOut } from 'next-auth/react';
import axios from 'axios';
import { storeUserToken } from 'store/slices/authSlice';
import { storeProvider } from 'store/slices/providerSlice';
import { storeCustomerDetails } from 'store/slices/userSlice';
import { storeAddresses } from 'store/slices/customerAddressSlice';
import { storeAllCartItems } from 'store/slices/cartSlice';
import { storeWishlist } from 'store/slices/productsSlice';
import { storeCompare } from 'store/slices/compareSlice';
import { userAPI } from 'APIs';
import { toast } from 'react-toastify';

const WithAuth = (Component: React.FC) => {
  const Auth = (props: any) => {
    const router = useRouter();
    const dispatch = useAppDispatch();
    const { data: session, status } = useSession();
    const [isChecking, setIsChecking] = useState(true);
    const [sessionHandled, setSessionHandled] = useState(false);
    const isLoggedIn = useAppSelector(
      (state) => state.persistedReducer.auth.access_token
    );
    const providerName = useAppSelector(
      (state) => state?.persistedReducer?.provider?.provider
    );

    const handleSession = useCallback(async () => {
      if (sessionHandled) return; // Prevent multiple calls

      console.log('🔍 WithAuth: Handling session...');
      setSessionHandled(true);

      try {
        // Check if we have a backend token from NextAuth session
        const backendToken = (session as any)?.backendToken;
        const backendUser = (session as any)?.backendUser;

        console.log(
          '🎫 Backend token from session:',
          backendToken ? 'Found' : 'Not found'
        );
        console.log(
          '👤 Backend user from session:',
          backendUser ? 'Found' : 'Not found'
        );

        if (backendToken) {
          // We have a backend token from NextAuth callback, use it directly
          console.log('✅ Using backend token from NextAuth session');
          dispatch(storeUserToken(backendToken));

          // Store provider information
          const provider = (session as any)?.account?.provider || 'google';
          dispatch(storeProvider(provider));

          // Fetch user data using the backend token
          const userResponse = await userAPI.getCustomer(backendToken);
          if ('data' in userResponse) {
            dispatch(storeCustomerDetails(userResponse.data));
            dispatch(storeAddresses(userResponse.data.addresses || []));
          }

          setIsChecking(false);
          // Don't show login success toast if this is a logout redirect
          if (!router.query.clear) {
            toast.success('Logged in successfully!', {
              containerId: 'bottom-right',
            });
          }

          // Clear NextAuth session after processing to make social signin behave like manual signin
          // This ensures status becomes 'unauthenticated' and session becomes false
          // So logout will work with single click like manual signin
          await signOut({ redirect: false });
          console.log(
            '🔄 Cleared NextAuth session to match manual signin flow'
          );
          return;
        }

        // Fallback: If no backend token in session, try the old method
        console.log(
          '🔄 No backend token in session, trying fallback method...'
        );

        const frontendUrl = process.env.NEXTAUTH_URL || 'http://localhost:4003';
        const accessToken = (
          await axios.get(`${frontendUrl}/api/auth/tokenHandler`)
        ).data.token;
        console.log(
          '🎫 Access token from tokenHandler:',
          accessToken ? 'Found' : 'Not found'
        );

        // Determine provider from session if not in Redux
        let sessionProvider = 'google';
        if (session?.user?.email) {
          sessionProvider = 'google';
        } else {
          sessionProvider = 'facebook';
        }

        const actualProvider = providerName || sessionProvider;
        console.log('🏪 Using provider:', actualProvider);

        let url: string;
        if (actualProvider === 'google') {
          url = '/api/googlesignin';
        } else if (actualProvider === 'facebook') {
          url = '/api/facebooksignin';
        } else if (actualProvider === 'apple') {
          url = '/api/applesignin';
        } else {
          console.log('❌ Unsupported provider in withAuth:', actualProvider);
          return;
        }

        const tokenfromBE = await fetch(url, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(accessToken),
        });

        const res = await tokenfromBE.json();
        console.log('🔄 Backend response:', res);
        console.log('🔄 Response status:', tokenfromBE.status);

        // Handle successful authentication (accept both 200 and 201)
        if (
          (tokenfromBE.status === 200 || tokenfromBE.status === 201) &&
          'data' in res &&
          res.data?.token
        ) {
          console.log('✅ Storing token in Redux...');
          dispatch(storeUserToken(res.data.token));

          // Fetch user data
          const userResponse = await userAPI.getCustomer(res.data.token);
          if ('data' in userResponse) {
            dispatch(storeCustomerDetails(userResponse.data));
            dispatch(storeAddresses(userResponse.data.addresses || []));
          }

          setIsChecking(false);
          // Don't show login success toast if this is a logout redirect
          if (!router.query.clear) {
            toast.success('Logged in successfully!', {
              containerId: 'bottom-right',
            });
          }

          // Clear NextAuth session after processing to make social signin behave like manual signin
          // This ensures status becomes 'unauthenticated' and session becomes false
          // So logout will work with single click like manual signin
          await signOut({ redirect: false });
          console.log(
            '🔄 Cleared NextAuth session to match manual signin flow'
          );
          return;
        }

        // Handle authentication failures
        console.log('❌ Backend authentication failed');
        setIsChecking(false);
        setSessionHandled(true);

        // Clear the NextAuth session since backend authentication failed
        await signOut({ redirect: false });

        toast.error('Authentication failed. Please try again.', {
          containerId: 'bottom-right',
        });
        router.push('/account/sign-in');
      } catch (error) {
        console.error('💥 Session handling error:', error);
        setIsChecking(false);
        setSessionHandled(true);

        // Clear the NextAuth session on error
        await signOut({ redirect: false });

        toast.error('Session handling failed. Please try again.', {
          containerId: 'bottom-right',
        });
        router.push('/account/sign-in');
      }
    }, [sessionHandled, session, providerName, dispatch, router]);

    useEffect(() => {
      console.log('🔐 WithAuth check:', {
        status,
        session: !!session,
        isLoggedIn: !!isLoggedIn,
        isChecking,
        sessionHandled,
      });

      // If NextAuth is still loading, wait
      if (status === 'loading') {
        return;
      }

      // If we have a token, we're authenticated - stop checking
      if (isLoggedIn) {
        console.log('✅ User is authenticated, allowing access');
        if (isChecking) {
          setIsChecking(false);
        }
        return;
      }

      // If we have a session but no Redux token, handle the session
      if (session && !isLoggedIn && !sessionHandled) {
        console.log('🔄 Session found but no Redux token, handling session...');
        handleSession();
        return;
      }

      // If no session and no token, redirect to sign in (only once)
      if (!session && !isLoggedIn && !sessionHandled) {
        console.log('🚫 No session and no token, redirecting to sign-in');
        setSessionHandled(true);
        router.push('/account/sign-in');
        return;
      }

      // Default case - stop checking
      if (isChecking) {
        setIsChecking(false);
      }
    }, [
      session,
      isLoggedIn,
      status,
      router,
      sessionHandled,
      handleSession,
      isChecking,
    ]);

    // Show loading while checking authentication
    if (status === 'loading' || isChecking) {
      console.log('🔄 Showing loading:', { status, isChecking });
      return <Loading />;
    }

    // If not logged in, show loading (will redirect)
    if (!isLoggedIn) {
      console.log('🚫 Not logged in, showing loading');
      return <Loading />;
    }

    // Render the component if authenticated
    console.log('🎉 Rendering protected component');
    return <Component {...props} />;
  };

  return Auth;
};

export default WithAuth;
