import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';

import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store/hooks/index';

import Loading from '@/modules/common/loader';
import { HeaderPage } from '@/modules/landingPage/components/First';
import { signIn } from 'next-auth/react';
import Image from 'next/image';
import { storeProvider } from 'store/slices/providerSlice';
import { SignInForm } from './SignInForm';

const Signin: React.FC = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { t } = useTranslation();

  const [loader, setLoader] = useState(false);
  const [googleLoading, setGoogleLoading] = useState(false);
  const [appleLoading, setAppleLoading] = useState(false);

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  // Redirect to newsfeed if user is already logged in
  useEffect(() => {
    if (token && token !== '') {
      console.log('🔄 User already logged in, redirecting to newsfeed...');
      // Small delay to prevent conflicts with withAuth
      const timer = setTimeout(() => {
        router.replace('/pricing');
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [token, router]);

  if (loader) return <Loading />;
  return (
    <>
      {/* <Breadcrumb
        title={`${t('common:account')}`}
        pathArray={[`${t('common:home')}`, `${t('common:account')}`]}
        linkArray={['/market', '/account/sign-in']}
      /> */}
      <HeaderPage />
      <div className="flex items-center justify-center">
        <Image
          src="/auth.png"
          alt="auth"
          width={400}
          height={150}
          className="hidden lg:block"
        />
        <div className="flex flex-wrap justify-center">
          <div
            className=" mx-3 flex w-full max-w-lg flex-col py-7 px-4 sm:px-6 md:px-8"
            // style={{
            //   width: ' 35rem ',
            //   height: 'auto',
            //   // background: '#f3f3f3',
            // }}
          >
            <h2 className="mx-3 text-center text-3xl font-bold text-black">
              {/* {t('common:login')} */}
              Welcome to fitsomnia
            </h2>
            <p className="mx-5 mt-2 mb-6 text-center text-black">
              {/* {t('login:login_form_header')} */}
              The ultimate all in one social media app
            </p>
            <h2 className="mx-3 mt-10 text-center text-3xl font-bold text-black">
              Login
            </h2>

            <div className="m-5 my-3 sm:m-5 md:mx-10 lg:mx-16 xl:mx-16">
              <SignInForm setLoader={setLoader} />
              <div>
                <p className="text-center">Or Continue With</p>
                <div className="my-4 flex items-center justify-center gap-5">
                  <button
                    className={`h-8 w-8 ${
                      googleLoading ? 'cursor-not-allowed opacity-75' : ''
                    }`}
                    disabled={googleLoading}
                    onClick={async () => {
                      setGoogleLoading(true);
                      try {
                        dispatch(storeProvider('google'));
                        await signIn('google', { callbackUrl: '/pricing' });
                      } finally {
                        setGoogleLoading(false);
                      }
                    }}
                  >
                    <Image
                      src="/google.png"
                      alt="Google signin"
                      width={200}
                      height={200}
                    />
                  </button>

                  <button
                    className={`h-8 w-8 ${
                      appleLoading ? 'cursor-not-allowed opacity-75' : ''
                    }`}
                    disabled={appleLoading}
                    onClick={async () => {
                      setAppleLoading(true);
                      try {
                        dispatch(storeProvider('apple'));
                        await signIn('apple', { callbackUrl: '/pricing' });
                      } finally {
                        setAppleLoading(false);
                      }
                    }}
                  >
                    <Image
                      src="/apple.png"
                      alt="Apple signin"
                      width={200}
                      height={200}
                    />
                  </button>

                  {/* <button className="h-8 w-8">
                    <Image
                      src="/facebook.png"
                      alt="auth"
                      width={200}
                      height={200}
                    />
                  </button> */}
                </div>
                <div className="flex items-center justify-center gap-2">
                  <p>Don’t have an account?</p>
                  <div>
                    <Link
                      prefetch={false}
                      data-testid="create-account-link"
                      href="/account/sign-up"
                      className="text-primary underline"
                    >
                      Create Account
                    </Link>
                  </div>
                </div>
              </div>

              {/* <NewForm /> */}
              {/* <div className="flex flex-row"> */}

              {/* <div className="text-decoration-none font-weight-light mt-3 text-gray-600 hover:text-primary">
                <Link
                  prefetch={false}
                  data-testid="create-account-link"
                  href="/account/sign-up"
                >
                  {t('login:create_account')}
                </Link>
              </div> */}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Signin;
