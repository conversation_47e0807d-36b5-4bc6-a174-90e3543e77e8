import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Set the correct content type for Apple app site association
  res.setHeader('Content-Type', 'application/json');
  res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache for 24 hours
  
  // Return the apple-app-site-association content
  const appleAppSiteAssociation = {
    "applinks": {
      "apps": [],
      "details": [
        {
          "appIDs": [
            "A4K2JJ9M2K.com.fitsomnia.fitsomniaApp"
          ],
          "components": [
            {
              "/": "/login",
              "comment": "Matches URL with a path /login"
            },
            {
              "/": "/events/*",
              "comment": "Matches any URL with a path that starts with /product/."
            }
          ]
        }
      ]
    }
  };
  
  res.status(200).json(appleAppSiteAssociation);
}
