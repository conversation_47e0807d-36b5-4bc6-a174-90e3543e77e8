import jwt from 'jsonwebtoken';

const { APPLE_TEAM_ID, APPLE_KEY_ID, APPLE_CLIENT_ID, APPLE_PRIVATE_KEY } =
  process.env;

/**
 * Generate a fresh Apple client secret JWT
 */
export function generateAppleClientSecret() {
  const now = Math.floor(Date.now() / 1000);
  const exp = now + 86400 * 180; // 6 months

  if (
    !APPLE_TEAM_ID ||
    !APPLE_KEY_ID ||
    !APPLE_CLIENT_ID ||
    !APPLE_PRIVATE_KEY
  ) {
    console.error('❌ Missing Apple environment variables:', {
      APPLE_TEAM_ID: !!APPLE_TEAM_ID,
      APPLE_KEY_ID: !!APPLE_KEY_ID,
      APPLE_CLIENT_ID: !!APPLE_CLIENT_ID,
      APPLE_PRIVATE_KEY: !!APPLE_PRIVATE_KEY && APPLE_PRIVATE_KEY.length > 0,
    });
    throw new Error('Missing required Apple environment variables');
  }

  console.log('🔐 Generating Apple client secret with:', {
    teamId: APPLE_TEAM_ID,
    keyId: APPLE_KEY_ID,
    clientId: APPLE_CLIENT_ID,
    expiresAt: new Date(exp * 1000).toISOString(),
  });

  try {
    const clientSecret = jwt.sign(
      {
        iss: APPLE_TEAM_ID,
        iat: now,
        exp,
        aud: 'https://appleid.apple.com',
        sub: APPLE_CLIENT_ID,
      },
      APPLE_PRIVATE_KEY.replace(/\\n/g, '\n'), // Fix newlines from env
      {
        algorithm: 'ES256', // Top-level: Specifies the signing algorithm
        keyid: APPLE_KEY_ID, // Top-level: Maps to header.kid
      }
    );

    console.log(
      '✅ Apple client secret generated successfully, length:',
      clientSecret.length
    );
    return clientSecret;
  } catch (error) {
    console.error('❌ Failed to generate Apple client secret:', error);
    throw error;
  }
}
