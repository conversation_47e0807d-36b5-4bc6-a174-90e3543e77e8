import { signOut } from 'next-auth/react';
import { clearAuthState } from '../store/slices/authSlice';
import { store } from '../store/store';

export const validateSession = async (): Promise<boolean> => {
  const state = store.getState();
  const token = state.persistedReducer.auth.access_token;

  if (!token) return false;

  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_PREFIX_REST}/user`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (response.status === 401) {
      // Session is invalid - user logged in from another device

      console.log('Session invalid, logging out...', response.status);
      await handleSessionInvalidation();
      return false;
    }

    return response.ok;
  } catch (error) {
    console.error('Session validation error:', error);
    return true; // Don't logout on network errors
  }
};

export const handleSessionInvalidation = async () => {
  // Clear Redux store
  store.dispatch(clearAuthState());

  // Clear NextAuth session if using social auth
  await signOut({ redirect: false });

  // Clear local storage
  localStorage.clear();
  sessionStorage.clear();

  // Redirect to login with message
  window.location.href = '/account/sign-in?clear=true';
};
