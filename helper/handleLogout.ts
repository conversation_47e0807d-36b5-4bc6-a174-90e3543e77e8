import { NextRouter } from 'next/router';
import { toast } from 'react-toastify';
import { clearAuthState } from 'store/slices/authSlice';
import { resetCart } from 'store/slices/cartSlice';
import { deleteCheckoutInfo } from 'store/slices/checkoutSlice';
import { resetCompare } from 'store/slices/compareSlice';
import { resetAddress } from 'store/slices/customerAddressSlice';
import { resetWishilist } from 'store/slices/productsSlice';
import { storeProvider } from 'store/slices/providerSlice';
import { resetUserDetails } from 'store/slices/userSlice';

export const handleLogout = (
  localStorage: Storage,
  dispatch: any,
  router: NextRouter
) => {
  // Clear Redux state immediately (synchronously) to update UI
  dispatch(clearAuthState());
  dispatch(resetAddress());
  dispatch(resetUserDetails());
  dispatch(resetWishilist());
  dispatch(resetCart());
  dispatch(resetCompare());
  dispatch(deleteCheckoutInfo());
  dispatch(storeProvider('none'));

  // Clear localStorage immediately
  localStorage.removeItem('persist:root');
  localStorage.clear();

  // Show success message and redirect
  toast.error('Logged out successfully!', {
    containerId: 'bottom-right',
  });

  router.push('/account/sign-in?clear=true');
};
