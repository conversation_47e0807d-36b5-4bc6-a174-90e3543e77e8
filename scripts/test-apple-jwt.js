const jwt = require('jsonwebtoken');
const crypto = require('crypto');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const {
  APPLE_TEAM_ID,
  APPLE_KEY_ID,
  APPLE_CLIENT_ID,
  APPLE_PRIVATE_KEY,
  NEXTAUTH_URL,
} = process.env;

console.log('🍎 Apple JWT Secret Generation & Validation Test');
console.log('=================================================');

function generateAppleClientSecret() {
  const now = Math.floor(Date.now() / 1000);
  const exp = now + 86400 * 180; // 6 months

  if (!APPLE_TEAM_ID || !APPLE_KEY_ID || !APPLE_CLIENT_ID || !APPLE_PRIVATE_KEY) {
    throw new Error("Missing required Apple environment variables");
  }

  return jwt.sign(
    {
      iss: APPLE_TEAM_ID,
      iat: now,
      exp,
      aud: "https://appleid.apple.com",
      sub: APPLE_CLIENT_ID,
    },
    APPLE_PRIVATE_KEY.replace(/\\n/g, "\n"),
    {
      algorithm: "ES256",
      keyid: APPLE_KEY_ID,
    }
  );
}

function validateEnvironmentVariables() {
  console.log('\n📋 Environment Variables Check:');
  
  const checks = {
    'APPLE_TEAM_ID': APPLE_TEAM_ID,
    'APPLE_KEY_ID': APPLE_KEY_ID,
    'APPLE_CLIENT_ID': APPLE_CLIENT_ID,
    'APPLE_PRIVATE_KEY': APPLE_PRIVATE_KEY,
    'NEXTAUTH_URL': NEXTAUTH_URL,
  };

  let allValid = true;
  
  for (const [key, value] of Object.entries(checks)) {
    const isValid = !!value;
    console.log(`  ${key}: ${isValid ? '✅' : '❌'} ${isValid ? 'Present' : 'Missing'}`);
    if (!isValid) allValid = false;
  }

  if (APPLE_CLIENT_ID) {
    const hasCorrectFormat = APPLE_CLIENT_ID.includes('.');
    console.log(`  Client ID Format: ${hasCorrectFormat ? '✅' : '❌'} ${hasCorrectFormat ? 'Valid' : 'Invalid (should contain dots)'}`);
    if (!hasCorrectFormat) allValid = false;
  }

  if (APPLE_PRIVATE_KEY) {
    const hasBeginEnd = APPLE_PRIVATE_KEY.includes('BEGIN PRIVATE KEY') && APPLE_PRIVATE_KEY.includes('END PRIVATE KEY');
    console.log(`  Private Key Format: ${hasBeginEnd ? '✅' : '❌'} ${hasBeginEnd ? 'Valid PEM format' : 'Invalid format'}`);
    if (!hasBeginEnd) allValid = false;
  }

  return allValid;
}

function validateJWTStructure(token) {
  console.log('\n🔍 JWT Structure Validation:');
  
  try {
    // Decode without verification to check structure
    const decoded = jwt.decode(token, { complete: true });
    
    if (!decoded) {
      console.log('  ❌ Failed to decode JWT');
      return false;
    }

    console.log('  ✅ JWT successfully decoded');
    
    // Check header
    const header = decoded.header;
    console.log('\n  📄 Header:');
    console.log(`    Algorithm: ${header.alg === 'ES256' ? '✅' : '❌'} ${header.alg}`);
    console.log(`    Type: ${header.typ === 'JWT' ? '✅' : '❌'} ${header.typ}`);
    console.log(`    Key ID: ${header.kid === APPLE_KEY_ID ? '✅' : '❌'} ${header.kid}`);
    
    // Check payload
    const payload = decoded.payload;
    console.log('\n  📄 Payload:');
    console.log(`    Issuer: ${payload.iss === APPLE_TEAM_ID ? '✅' : '❌'} ${payload.iss}`);
    console.log(`    Subject: ${payload.sub === APPLE_CLIENT_ID ? '✅' : '❌'} ${payload.sub}`);
    console.log(`    Audience: ${payload.aud === 'https://appleid.apple.com' ? '✅' : '❌'} ${payload.aud}`);
    console.log(`    Issued At: ✅ ${new Date(payload.iat * 1000).toISOString()}`);
    console.log(`    Expires At: ✅ ${new Date(payload.exp * 1000).toISOString()}`);
    
    // Check if token is expired
    const now = Math.floor(Date.now() / 1000);
    const isExpired = payload.exp < now;
    console.log(`    Is Expired: ${isExpired ? '❌' : '✅'} ${isExpired ? 'Yes' : 'No'}`);
    
    return !isExpired;
    
  } catch (error) {
    console.log(`  ❌ JWT validation failed: ${error.message}`);
    return false;
  }
}

function testPrivateKeyFormat() {
  console.log('\n🔐 Private Key Format Test:');
  
  try {
    const privateKey = APPLE_PRIVATE_KEY.replace(/\\n/g, "\n");
    
    // Try to create a test signature
    const testPayload = { test: 'signature' };
    const testToken = jwt.sign(testPayload, privateKey, {
      algorithm: 'ES256',
      keyid: APPLE_KEY_ID,
    });
    
    console.log('  ✅ Private key format is valid');
    console.log('  ✅ Can create ES256 signatures');
    console.log(`  ✅ Test token length: ${testToken.length} characters`);
    
    return true;
    
  } catch (error) {
    console.log(`  ❌ Private key test failed: ${error.message}`);
    return false;
  }
}

function displayConfiguration() {
  console.log('\n⚙️  Apple Sign-In Configuration:');
  console.log(`  Service ID: ${APPLE_CLIENT_ID}`);
  console.log(`  Team ID: ${APPLE_TEAM_ID}`);
  console.log(`  Key ID: ${APPLE_KEY_ID}`);
  console.log(`  Domain: ${NEXTAUTH_URL ? new URL(NEXTAUTH_URL).hostname : 'Not set'}`);
  console.log(`  Redirect URI: ${NEXTAUTH_URL}/api/auth/callback/apple`);
}

function runTests() {
  try {
    // Test 1: Environment Variables
    const envValid = validateEnvironmentVariables();
    if (!envValid) {
      console.log('\n❌ Environment validation failed. Please check your .env.local file.');
      return;
    }

    // Test 2: Private Key Format
    const keyValid = testPrivateKeyFormat();
    if (!keyValid) {
      console.log('\n❌ Private key validation failed. Please check your APPLE_PRIVATE_KEY.');
      return;
    }

    // Test 3: Generate JWT
    console.log('\n🔄 Generating Apple Client Secret...');
    const clientSecret = generateAppleClientSecret();
    console.log(`  ✅ Client secret generated successfully`);
    console.log(`  ✅ Length: ${clientSecret.length} characters`);

    // Test 4: Validate JWT Structure
    const jwtValid = validateJWTStructure(clientSecret);
    if (!jwtValid) {
      console.log('\n❌ JWT validation failed.');
      return;
    }

    // Test 5: Display Configuration
    displayConfiguration();

    // Test 6: Apple Developer Console Checklist
    console.log('\n📝 Apple Developer Console Checklist:');
    console.log('  □ Service ID exists and is configured for Sign In with Apple');
    console.log('  □ Domain is verified (root domain must be verified first)');
    console.log('  □ Return URL is configured correctly');
    console.log('  □ Private key is associated with the Key ID');
    console.log('  □ Primary App ID is linked to the Service ID');

    console.log('\n🎉 All tests passed! Your Apple JWT configuration appears to be correct.');
    console.log('\nIf you\'re still getting invalid_grant errors, the issue is likely in:');
    console.log('1. Apple Developer Console domain verification');
    console.log('2. Return URL configuration mismatch');
    console.log('3. Service ID not properly enabled for Sign In with Apple');

  } catch (error) {
    console.error('\n💥 Test failed with error:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the tests
runTests();
